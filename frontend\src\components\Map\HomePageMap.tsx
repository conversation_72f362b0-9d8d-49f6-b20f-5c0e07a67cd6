import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, message, Drawer, Typography, Space, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import L from 'leaflet';
import BaseMap from './BaseMap';
import HeatmapLayer, { generateMockHeatmapData } from './HeatmapLayer';

// 导入GridCell类型
interface GridCell {
  bounds: L.LatLngBounds;
  intensity: number;
  count: number;
  points: HeatmapPoint[];
}
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { setMapCenter, setMapZoom } from '../../store/slices/uiSlice';

const { Text } = Typography;

interface HeatmapPoint {
  lat: number;
  lng: number;
  intensity: number;
  species_count?: number;
  recording_count?: number;
}

interface HomePageMapProps {
  height?: string;
  onDataLoad?: (data: any) => void;
}

const HomePageMap: React.FC<HomePageMapProps> = ({
  height = '100%',
  onDataLoad,
}) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // Redux状态
  const { mapCenter, mapZoom } = useAppSelector(state => state.ui);
  
  // 本地状态
  const [map, setMap] = useState<L.Map | null>(null);
  const [heatmapData, setHeatmapData] = useState<HeatmapPoint[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPoint, setSelectedPoint] = useState<HeatmapPoint | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  
  // 使用ref存储回调函数，避免依赖数组问题
  const onDataLoadRef = useRef(onDataLoad);
  onDataLoadRef.current = onDataLoad;

  // 加载热力图数据
  useEffect(() => {
    const loadHeatmapData = async () => {
      setLoading(true);
      try {
        // 这里应该从API获取真实数据
        // const response = await fetch('/api/heatmap-data');
        // const data = await response.json();
        
        // 暂时使用模拟数据 - 减少数据量以提高性能
        const mockData = generateMockHeatmapData(300);
        setHeatmapData(mockData);
        
        // 通知父组件数据已加载
        if (onDataLoadRef.current) {
          const stats = {
            totalPoints: mockData.length,
            totalSpecies: mockData.reduce((sum, point) => sum + (point.species_count || 0), 0),
            totalRecordings: mockData.reduce((sum, point) => sum + (point.recording_count || 0), 0),
            activeHotspots: mockData.filter(point => point.intensity > 0.7).length,
          };
          onDataLoadRef.current(stats);
        }
      } catch (error) {
        console.error('加载热力图数据失败:', error);
        message.error('加载地图数据失败');
      } finally {
        setLoading(false);
      }
    };

    loadHeatmapData();
  }, []); // 移除onDataLoad依赖，因为数据只需要加载一次

  // 地图就绪回调 - 简化版本，事件监听器在useEffect中管理
  const handleMapReady = useCallback((mapInstance: L.Map) => {
    setMap(mapInstance);
  }, []);

  // 管理地图事件监听器的清理
  useEffect(() => {
    if (!map) return;

    let cleanup: (() => void) | undefined;

    // 重新设置事件监听器
    const setupEventListeners = () => {
      const handleMoveEnd = () => {
        const center = map.getCenter();
        dispatch(setMapCenter([center.lat, center.lng]));
      };

      const handleZoomEnd = () => {
        dispatch(setMapZoom(map.getZoom()));
      };

      map.on('moveend', handleMoveEnd);
      map.on('zoomend', handleZoomEnd);

      return () => {
        map.off('moveend', handleMoveEnd);
        map.off('zoomend', handleZoomEnd);
      };
    };

    cleanup = setupEventListeners();

    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, [map, dispatch]);

  // 热力图网格点击处理
  const handlePointClick = useCallback((cell: GridCell) => {
    // 从网格中选择第一个点作为代表点显示详情
    if (cell.points.length > 0) {
      const representativePoint = cell.points[0];
      // 添加网格统计信息
      const enhancedPoint = {
        ...representativePoint,
        species_count: cell.points.reduce((sum, p) => sum + (p.species_count || 0), 0),
        recording_count: cell.points.reduce((sum, p) => sum + (p.recording_count || 0), 0),
        intensity: cell.intensity,
        gridInfo: {
          totalPoints: cell.count,
          avgIntensity: cell.intensity,
          bounds: cell.bounds
        }
      };
      setSelectedPoint(enhancedPoint);
      setDrawerVisible(true);
    }
  }, []);

  // 关闭详情抽屉
  const handleCloseDrawer = () => {
    setDrawerVisible(false);
    setSelectedPoint(null);
  };



  return (
    <div style={{
      position: 'relative',
      height,
      width: '100%',
      overflow: 'hidden'
    }}>
      {/* 加载状态覆盖层 */}
      {loading && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(255, 255, 255, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: '8px' }}>🔄</div>
            <div>正在加载监测数据...</div>
          </div>
        </div>
      )}

      {/* 地图容器 */}
      <BaseMap
        center={mapCenter}
        zoom={mapZoom}
        style={{ height: '100%', width: '100%' }}
        onMapReady={handleMapReady}
      >
        {/* 热力图图层 */}
        {heatmapData.length > 0 && (
          <HeatmapLayer
            data={heatmapData}
            onCellClick={handlePointClick}
            options={{
              gridSize: 0.3,
              minOpacity: 0.2,
              maxOpacity: 0.8,
              gradient: {
                '0.0': '#313695',
                '0.1': '#4575b4',
                '0.2': '#74add1',
                '0.3': '#abd9e9',
                '0.4': '#e0f3f8',
                '0.5': '#ffffbf',
                '0.6': '#fee090',
                '0.7': '#fdae61',
                '0.8': '#f46d43',
                '0.9': '#d73027',
                '1.0': '#a50026'
              }
            }}
          />
        )}
      </BaseMap>

      {/* 点击详情抽屉 */}
      <Drawer
        title="热点详情"
        placement="right"
        onClose={handleCloseDrawer}
        open={drawerVisible}
        width={400}
      >
        {selectedPoint && (
          <div>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {/* 位置信息 */}
              <Card size="small" title="位置信息">
                <div style={{ marginBottom: '8px' }}>
                  <Text strong>纬度：</Text>
                  <Text>{selectedPoint.lat.toFixed(4)}°</Text>
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <Text strong>经度：</Text>
                  <Text>{selectedPoint.lng.toFixed(4)}°</Text>
                </div>
                <div>
                  <Text strong>数据密度：</Text>
                  <Text>{(selectedPoint.intensity * 100).toFixed(1)}%</Text>
                </div>
              </Card>

              {/* 统计信息 */}
              <Card size="small" title="统计数据">
                <div style={{ marginBottom: '8px' }}>
                  <Text strong>物种数量：</Text>
                  <Text>{selectedPoint.species_count || 0} 种</Text>
                </div>
                <div>
                  <Text strong>录音数量：</Text>
                  <Text>{selectedPoint.recording_count || 0} 个</Text>
                </div>
              </Card>

              {/* 操作按钮 */}
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button 
                  type="primary" 
                  block
                  onClick={() => {
                    // 跳转到该区域的详细页面
                    navigate(`/map?lat=${selectedPoint.lat}&lng=${selectedPoint.lng}&zoom=12`);
                  }}
                >
                  查看详细地图
                </Button>
                <Button 
                  block
                  onClick={() => {
                    // 跳转到该区域的物种列表
                    navigate(`/species?lat=${selectedPoint.lat}&lng=${selectedPoint.lng}`);
                  }}
                >
                  查看区域物种
                </Button>
              </Space>
            </Space>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default HomePageMap;
