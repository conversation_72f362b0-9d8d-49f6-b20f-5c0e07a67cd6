import React, { useState, useEffect, useCallback } from 'react';
import { Card, message, Drawer } from 'antd';
import { useNavigate } from 'react-router-dom';
import L from 'leaflet';
import BaseMap from './BaseMap';
import DistributionLayer from './DistributionLayer';
import CollectionPointsLayer from './CollectionPointsLayer';
import MapControls, { MapLegend } from './MapControls';
import MapServiceSelector from './MapServiceSelector';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { setMapMode, setMapCenter, setMapZoom } from '../../store/slices/uiSlice';
import { MapServiceType } from '../../config/mapConfig';

interface InteractiveMapProps {
  height?: string;
  showControls?: boolean;
}

const InteractiveMap: React.FC<InteractiveMapProps> = ({
  height = '600px',
  showControls = true,
}) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  // Redux状态
  const { mapMode, mapCenter, mapZoom } = useAppSelector(state => state.ui);
  
  // 本地状态
  const [map, setMap] = useState<L.Map | null>(null);
  const [distributionData, setDistributionData] = useState([]);
  const [collectionPoints, setCollectionPoints] = useState([]);
  const [loading, setLoading] = useState(false);
  const [legendVisible, setLegendVisible] = useState(false);
  const [selectedPoint, setSelectedPoint] = useState<any>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [mapService, setMapService] = useState<MapServiceType>(MapServiceType.AMAP);

  // 获取分布数据
  const fetchDistributionData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/geography/distributions`);
      if (response.ok) {
        const data = await response.json();
        setDistributionData(data.data || []);
      }
    } catch (error) {
      console.error('获取分布数据失败:', error);
      message.error('获取分布数据失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取采集点数据
  const fetchCollectionPoints = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/geography/collection-points`);
      if (response.ok) {
        const data = await response.json();
        setCollectionPoints(data.data || []);
      }
    } catch (error) {
      console.error('获取采集点数据失败:', error);
      message.error('获取采集点数据失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    if (mapMode === 'distribution') {
      fetchDistributionData();
    } else {
      fetchCollectionPoints();
    }
  }, [mapMode, fetchDistributionData, fetchCollectionPoints]);

  // 地图模式切换
  const handleModeChange = (mode: 'distribution' | 'collection') => {
    dispatch(setMapMode(mode));
  };

  // 地图就绪回调
  const handleMapReady = (mapInstance: L.Map) => {
    setMap(mapInstance);
    
    // 监听地图移动和缩放事件
    mapInstance.on('moveend', () => {
      const center = mapInstance.getCenter();
      dispatch(setMapCenter([center.lat, center.lng]));
    });
    
    mapInstance.on('zoomend', () => {
      dispatch(setMapZoom(mapInstance.getZoom()));
    });
  };

  // 重置地图视图
  const handleReset = () => {
    if (map) {
      map.setView([30.0, 120.0], 5);
    }
  };

  // 全屏切换
  const handleFullscreen = () => {
    if (map) {
      const mapContainer = map.getContainer().parentElement;
      if (mapContainer) {
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          mapContainer.requestFullscreen();
        }
      }
    }
  };

  // 物种点击处理
  const handleSpeciesClick = (speciesId: string) => {
    navigate(`/species/${speciesId}`);
  };

  // 采集点点击处理
  const handlePointClick = (point: any) => {
    setSelectedPoint(point);
    setDrawerVisible(true);
  };

  return (
    <div style={{ position: 'relative' }}>
      {/* 地图控制面板 */}
      {showControls && (
        <MapControls
          mode={mapMode}
          onModeChange={handleModeChange}
          onReset={handleReset}
          onFullscreen={handleFullscreen}
          onShowLegend={() => setLegendVisible(true)}
          distributionCount={distributionData.length}
          collectionCount={collectionPoints.length}
        />
      )}

      {/* 地图容器 */}
      <Card loading={loading} style={{ position: 'relative' }}>
        {/* 地图服务选择器 */}
        <div style={{
          position: 'absolute',
          top: 10,
          right: 10,
          zIndex: 1000,
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderRadius: '6px',
          padding: '8px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
        }}>
          <MapServiceSelector
            value={mapService}
            onChange={setMapService}
            size="small"
          />
        </div>

        <BaseMap
          center={mapCenter}
          zoom={mapZoom}
          style={{ height, width: '100%' }}
          onMapReady={handleMapReady}
          mapService={mapService}
        >
          {/* 分布范围图层 */}
          {mapMode === 'distribution' && (
            <DistributionLayer
              data={distributionData}
              onSpeciesClick={handleSpeciesClick}
            />
          )}

          {/* 采集点图层 */}
          {mapMode === 'collection' && (
            <CollectionPointsLayer
              points={collectionPoints}
              onPointClick={handlePointClick}
              onSpeciesClick={handleSpeciesClick}
            />
          )}
        </BaseMap>

        {/* 图例 */}
        <MapLegend
          mode={mapMode}
          visible={legendVisible}
          onClose={() => setLegendVisible(false)}
        />
      </Card>

      {/* 采集点详情抽屉 */}
      <Drawer
        title="采集点详情"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={400}
      >
        {selectedPoint && (
          <div>
            {/* 这里可以添加更详细的采集点信息 */}
            <p>物种: {selectedPoint.chinese_name}</p>
            <p>位置: {selectedPoint.recording_location}</p>
            <p>音频数量: {selectedPoint.audio_count}</p>
            {/* TODO: 添加音频文件列表 */}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default InteractiveMap;
