import React, { useState, useEffect } from 'react';
import { Card, Badge, Button, Space, Typography, Tooltip } from 'antd';
import { ReloadOutlined, CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { MapServiceType, getMapConfig } from '../../config/mapConfig';

const { Text } = Typography;

interface ServiceStatus {
  service: MapServiceType;
  status: 'checking' | 'online' | 'offline' | 'unknown';
  responseTime?: number;
  lastCheck?: Date;
  error?: string;
}

interface MapServiceHealthCheckProps {
  services?: MapServiceType[];
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const MapServiceHealthCheck: React.FC<MapServiceHealthCheckProps> = ({
  services = [
    MapServiceType.AMAP,
  ],
  autoRefresh = false,
  refreshInterval = 30000, // 30秒
}) => {
  const [serviceStatuses, setServiceStatuses] = useState<ServiceStatus[]>(
    services.map(service => ({
      service,
      status: 'unknown',
    }))
  );

  // 检查单个地图服务的健康状态
  const checkServiceHealth = async (service: MapServiceType): Promise<ServiceStatus> => {
    const startTime = Date.now();
    
    try {
      const config = getMapConfig(service);
      const baseUrl = config.base.url;
      
      // 构造一个测试瓦片URL（世界地图中心，低缩放级别）
      let testUrl = baseUrl
        .replace('{z}', '2')
        .replace('{x}', '2')
        .replace('{y}', '1')
        .replace('{s}', config.base.subdomains?.[0] || 'a')
        .replace('{r}', '');



      // 使用fetch检查瓦片是否可访问
      const response = await fetch(testUrl, {
        method: 'HEAD', // 只检查头部，不下载完整内容
        mode: 'no-cors', // 避免CORS问题
        cache: 'no-cache',
      });

      const responseTime = Date.now() - startTime;

      return {
        service,
        status: 'online',
        responseTime,
        lastCheck: new Date(),
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        service,
        status: 'offline',
        responseTime,
        lastCheck: new Date(),
        error: error instanceof Error ? error.message : '连接失败',
      };
    }
  };

  // 检查所有服务的健康状态
  const checkAllServices = async () => {
    // 设置所有服务为检查中状态
    setServiceStatuses(prev => 
      prev.map(status => ({ ...status, status: 'checking' as const }))
    );

    // 并行检查所有服务
    const promises = services.map(service => checkServiceHealth(service));
    const results = await Promise.all(promises);

    setServiceStatuses(results);
  };

  // 初始检查
  useEffect(() => {
    checkAllServices();
  }, []);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(checkAllServices, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  // 获取服务显示名称
  const getServiceDisplayName = (service: MapServiceType): string => {
    const names = {
      [MapServiceType.AMAP]: '高德地图',
    };
    return names[service] || service;
  };

  // 获取状态图标和颜色
  const getStatusDisplay = (status: ServiceStatus) => {
    switch (status.status) {
      case 'checking':
        return {
          icon: <LoadingOutlined spin />,
          color: 'processing',
          text: '检查中...',
        };
      case 'online':
        return {
          icon: <CheckCircleOutlined />,
          color: 'success',
          text: `在线 (${status.responseTime}ms)`,
        };
      case 'offline':
        return {
          icon: <CloseCircleOutlined />,
          color: 'error',
          text: '离线',
        };
      default:
        return {
          icon: null,
          color: 'default',
          text: '未知',
        };
    }
  };

  return (
    <Card
      title="地图服务状态"
      size="small"
      extra={
        <Button
          size="small"
          icon={<ReloadOutlined />}
          onClick={checkAllServices}
          loading={serviceStatuses.some(s => s.status === 'checking')}
        >
          刷新
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {serviceStatuses.map((status) => {
          const display = getStatusDisplay(status);
          
          return (
            <div
              key={status.service}
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '4px 0',
              }}
            >
              <Text style={{ fontSize: '12px' }}>
                {getServiceDisplayName(status.service)}
              </Text>
              
              <Tooltip
                title={
                  <div>
                    <div>状态: {display.text}</div>
                    {status.lastCheck && (
                      <div>检查时间: {status.lastCheck.toLocaleTimeString()}</div>
                    )}
                    {status.error && (
                      <div>错误: {status.error}</div>
                    )}
                  </div>
                }
              >
                <Badge
                  status={display.color as any}
                  text={
                    <span style={{ fontSize: '11px' }}>
                      {display.icon} {display.text}
                    </span>
                  }
                />
              </Tooltip>
            </div>
          );
        })}
        
        {autoRefresh && (
          <Text type="secondary" style={{ fontSize: '10px', textAlign: 'center' }}>
            自动刷新间隔: {refreshInterval / 1000}秒
          </Text>
        )}
      </Space>
    </Card>
  );
};

export default MapServiceHealthCheck;
