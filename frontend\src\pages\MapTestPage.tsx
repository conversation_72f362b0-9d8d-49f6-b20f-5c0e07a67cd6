import React, { useState } from 'react';
import { Card, Typography, Space, Alert, Row, Col, Button, Select, Tag } from 'antd';
import BaseMap from '../components/Map/BaseMap';
import MapSourceIndicator from '../components/Map/MapSourceIndicator';
import MapServiceHealthCheck from '../components/Map/MapServiceHealthCheck';
import { MapServiceType } from '../config/mapConfig';

const { Title, Paragraph } = Typography;

/**
 * 地图测试页面
 * 用于测试不同地图服务的加载和显示效果
 */
const MapTestPage: React.FC = () => {
  const [mapService, setMapService] = useState<MapServiceType>(MapServiceType.AMAP);
  const [mapCenter, setMapCenter] = useState<[number, number]>([35.0, 104.0]);
  const [mapZoom, setMapZoom] = useState(4);
  const [enableAutoSwitch, setEnableAutoSwitch] = useState(true);

  const handleMapServiceChange = (service: MapServiceType) => {
    setMapService(service);
  };

  // 地图服务选项
  const mapServiceOptions = [
    { value: MapServiceType.AMAP, label: '高德地图', description: '中国境内详细地图' },
  ];

  const handleResetView = () => {
    setMapCenter([35.0, 104.0]);
    setMapZoom(4);
  };

  const presetLocations = [
    // 中国境内位置（应使用高德地图）
    { name: '中国全境', center: [35.0, 104.0] as [number, number], zoom: 4, region: '中国' },
    { name: '北京', center: [39.9042, 116.4074] as [number, number], zoom: 10, region: '中国' },
    { name: '上海', center: [31.2304, 121.4737] as [number, number], zoom: 10, region: '中国' },
    { name: '广州', center: [23.1291, 113.2644] as [number, number], zoom: 10, region: '中国' },
    { name: '台湾', center: [23.8, 121.0] as [number, number], zoom: 8, region: '中国' },
    { name: '南海', center: [16.0, 112.0] as [number, number], zoom: 6, region: '中国' },

    // 全球位置（应自动切换到OpenStreetMap或CartoDB）
    { name: '纽约', center: [40.7128, -74.0060] as [number, number], zoom: 10, region: '美国' },
    { name: '伦敦', center: [51.5074, -0.1278] as [number, number], zoom: 10, region: '英国' },
    { name: '东京', center: [35.6762, 139.6503] as [number, number], zoom: 10, region: '日本' },
    { name: '悉尼', center: [-33.8688, 151.2093] as [number, number], zoom: 10, region: '澳大利亚' },
    { name: '巴黎', center: [48.8566, 2.3522] as [number, number], zoom: 10, region: '法国' },
    { name: '全球视图', center: [0, 0] as [number, number], zoom: 2, region: '全球' },
  ];

  return (
    <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={2}>地图服务测试页面</Title>
        
        <Alert
          message="地图服务合规性说明"
          description="本页面展示的所有地图服务均符合中华人民共和国测绘法和地图管理条例要求，确保台湾地区正确显示为中国领土的一部分。"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Row gutter={[16, 16]}>
          {/* 控制面板 */}
          <Col xs={24} lg={8}>
            <Card title="地图控制" style={{ marginBottom: '16px' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Paragraph strong>选择地图服务：</Paragraph>
                  <Select
                    value={mapService}
                    onChange={handleMapServiceChange}
                    style={{ width: '100%' }}
                    options={mapServiceOptions.map(option => ({
                      value: option.value,
                      label: (
                        <div>
                          <div>{option.label}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>{option.description}</div>
                        </div>
                      )
                    }))}
                  />
                </div>

                <div>
                  <Paragraph strong>自动切换：</Paragraph>
                  <Button
                    type={enableAutoSwitch ? 'primary' : 'default'}
                    onClick={() => setEnableAutoSwitch(!enableAutoSwitch)}
                    style={{ width: '100%' }}
                  >
                    {enableAutoSwitch ? '已启用自动切换' : '手动选择地图源'}
                  </Button>
                </div>

                <div>
                  <Paragraph strong>快速定位：</Paragraph>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Tag color="blue" style={{ marginBottom: '8px' }}>中国境内</Tag>
                      <Space wrap>
                        {presetLocations.filter(loc => loc.region === '中国').map((location) => (
                          <Button
                            key={location.name}
                            size="small"
                            onClick={() => {
                              setMapCenter(location.center);
                              setMapZoom(location.zoom);
                            }}
                          >
                            {location.name}
                          </Button>
                        ))}
                      </Space>
                    </div>
                    <div>
                      <Tag color="green" style={{ marginBottom: '8px' }}>全球位置</Tag>
                      <Space wrap>
                        {presetLocations.filter(loc => loc.region !== '中国').map((location) => (
                          <Button
                            key={location.name}
                            size="small"
                            onClick={() => {
                              setMapCenter(location.center);
                              setMapZoom(location.zoom);
                            }}
                          >
                            {location.name}
                          </Button>
                        ))}
                      </Space>
                    </div>
                  </Space>
                </div>

                <Button type="primary" onClick={handleResetView} block>
                  重置视图
                </Button>
              </Space>
            </Card>

            <Card title="当前配置" size="small">
              <Space direction="vertical" style={{ width: '100%', fontSize: '12px' }}>
                <div>地图服务: {mapService}</div>
                <div>中心点: [{mapCenter[0].toFixed(4)}, {mapCenter[1].toFixed(4)}]</div>
                <div>缩放级别: {mapZoom}</div>
              </Space>
            </Card>

            {/* 地图服务健康检查 */}
            <MapServiceHealthCheck
              autoRefresh={true}
              refreshInterval={30000}
            />
          </Col>

          {/* 地图显示区域 */}
          <Col xs={24} lg={16}>
            <Card
              title={`地图显示 - ${mapService} ${enableAutoSwitch ? '(自动切换)' : '(手动)'}`}
              style={{ height: '600px' }}
              styles={{ body: { padding: 0, height: 'calc(100% - 57px)' } }}
            >
              <div style={{ position: 'relative', height: '100%' }}>
                <BaseMap
                  center={mapCenter}
                  zoom={mapZoom}
                  style={{ height: '100%', width: '100%' }}
                  onMapReady={(map) => {
                    console.log('地图已准备就绪:', map);

                    // 监听地图移动事件
                    map.on('moveend', () => {
                      const center = map.getCenter();
                      setMapCenter([center.lat, center.lng]);
                    });

                    // 监听缩放事件
                    map.on('zoomend', () => {
                      setMapZoom(map.getZoom());
                    });
                  }}
                />

                {/* 地图源指示器 */}
                <MapSourceIndicator
                  position="bottom-right"
                />
              </div>
            </Card>
          </Col>
        </Row>

        {/* 说明信息 */}
        <Card title="使用说明" style={{ marginTop: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Title level={4}>支持的地图服务</Title>
              <ul>
                <li><strong>高德地图</strong>: 中国境内详细地图数据（默认，符合审查要求）</li>
                <li><strong>ESRI世界地图</strong>: 稳定可靠的全球地图服务（推荐用于境外）</li>
                <li><strong>CartoDB</strong>: 简洁全球地图样式（适合数据可视化）</li>
                <li><strong>OpenStreetMap</strong>: 全球开源地图数据（含多个镜像源）</li>
                <li><strong>Stamen</strong>: 简约美观的全球地图样式</li>
                <li><strong>天地图影像</strong>: 国家地理信息公共服务平台影像地图</li>
                <li><strong>天地图矢量</strong>: 国家地理信息公共服务平台矢量地图</li>
                <li><strong>百度地图</strong>: 百度地图服务（符合中国地图审查要求）</li>
              </ul>
            </Col>
            <Col xs={24} md={12}>
              <Title level={4}>智能切换与容错特性</Title>
              <ul>
                <li>🔄 <strong>自动切换</strong>: 根据地理位置自动选择最佳地图源</li>
                <li>🇨🇳 <strong>中国境内</strong>: 自动使用高德地图（符合审查要求）</li>
                <li>🌍 <strong>全球范围</strong>: 优先使用ESRI世界地图（稳定性更好）</li>
                <li>🛡️ <strong>容错机制</strong>: 瓦片加载失败时自动降级到备用服务</li>
                <li>🔁 <strong>智能重试</strong>: 失败瓦片自动重试加载（最多2次）</li>
                <li>📊 <strong>加载监控</strong>: 实时监控瓦片加载成功率</li>
                <li>⚡ <strong>性能优化</strong>: 根据缩放级别选择合适的地图服务</li>
                <li>🎛️ <strong>手动控制</strong>: 支持用户手动选择地图源</li>
              </ul>
            </Col>
          </Row>
        </Card>
      </div>
    </div>
  );
};

export default MapTestPage;
