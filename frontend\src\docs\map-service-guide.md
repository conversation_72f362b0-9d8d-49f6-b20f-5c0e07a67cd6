# 地图服务使用指南

## 概述

本系统提供了智能的地图服务切换功能，能够根据地理位置和网络状况自动选择最佳的地图服务，确保在全球范围内都能获得良好的地图体验。

## 支持的地图服务

### 中国境内服务
- **高德地图** (推荐)
  - 符合中国地图审查要求
  - 包含完整的中国国界（含台湾）
  - 中国境内数据最详细准确

- **天地图影像/矢量**
  - 国家地理信息公共服务平台
  - 官方权威地图数据
  - 需要API密钥

- **百度地图**
  - 符合中国地图审查要求
  - 备用选择

### 全球服务
- **ESRI世界地图** (推荐)
  - 稳定可靠的全球地图服务
  - 在中国大陆可正常访问
  - 数据覆盖全面

- **CartoDB**
  - 简洁美观的地图样式
  - 适合数据可视化
  - 加载速度快

- **OpenStreetMap**
  - 开源地图数据
  - 包含多个镜像源
  - 详细的全球数据

- **Stamen**
  - 简约美观的设计
  - 多种样式选择

## 智能切换机制

### 自动切换规则
1. **地理位置感知**
   - 中国境内：自动使用高德地图
   - 中国境外：优先使用ESRI世界地图

2. **缩放级别优化**
   - 低缩放级别（国家/大洲视图）：使用CartoDB
   - 高缩放级别（城市/街道视图）：使用ESRI或OSM

3. **网络状况适应**
   - 监控瓦片加载成功率
   - 失败率超过50%时自动降级

### 容错机制
1. **瓦片重试**
   - 失败瓦片自动重试（最多2次）
   - 递增延迟重试（1s, 2s）

2. **服务降级**
   - 主服务不可用时自动切换到备用服务
   - 按优先级顺序尝试备用服务

3. **实时监控**
   - 监控瓦片加载统计
   - 开发环境下显示详细日志

## 使用方法

### 基础使用
```tsx
import BaseMap from '../components/Map/BaseMap';
import { MapServiceType } from '../config/mapConfig';

<BaseMap
  center={[39.9042, 116.4074]}
  zoom={10}
  mapService={MapServiceType.AMAP}
  enableAutoSwitch={true}
  enableTileMonitor={true}
  onMapServiceChange={(newService) => {
    console.log('地图服务已切换到:', newService);
  }}
/>
```

### 高级配置
```tsx
<BaseMap
  center={[40.7128, -74.0060]} // 纽约
  zoom={12}
  enableAutoSwitch={true}      // 启用自动切换
  enableTileMonitor={true}     // 启用瓦片监控
  onMapServiceChange={(service, reason) => {
    // 处理服务切换事件
    console.log(`切换到 ${service}，原因: ${reason}`);
  }}
/>
```

### 手动控制
```tsx
import MapSourceIndicator from '../components/Map/MapSourceIndicator';

<MapSourceIndicator
  currentService={currentService}
  onServiceChange={handleServiceChange}
  showSwitcher={true}
  position="bottom-right"
/>
```

## 最佳实践

### 1. 地图服务选择
- **中国用户**：优先使用高德地图或天地图
- **国际用户**：优先使用ESRI世界地图
- **数据可视化**：推荐使用CartoDB或Stamen

### 2. 性能优化
- 启用瓦片监控以获得最佳性能
- 在移动设备上考虑禁用自动切换以节省流量
- 使用适当的缩放级别限制

### 3. 错误处理
- 监听`onMapServiceChange`事件
- 提供用户友好的错误提示
- 实现备用方案

### 4. 合规性考虑
- 在中国境内必须使用符合审查要求的地图服务
- 确保台湾地区正确显示
- 遵守相关法律法规

## 故障排除

### 常见问题

1. **OpenStreetMap无法加载**
   - 原因：网络限制或服务器不可用
   - 解决：系统会自动切换到ESRI或CartoDB

2. **天地图需要API密钥**
   - 原因：未配置VITE_TIANDITU_KEY环境变量
   - 解决：申请天地图API密钥并配置

3. **地图显示空白**
   - 原因：所有地图服务都不可用
   - 解决：检查网络连接，手动切换地图服务

4. **自动切换过于频繁**
   - 原因：网络不稳定
   - 解决：禁用自动切换，手动选择稳定的服务

### 调试工具

1. **开发环境日志**
   - 瓦片加载统计每10秒输出一次
   - 包含成功率、失败率等信息

2. **地图服务健康检查**
   - 实时检查各服务可用性
   - 显示响应时间和错误信息

3. **浏览器开发者工具**
   - 查看网络请求
   - 检查瓦片加载错误

## 更新日志

### v1.0.0 (2024-01-19)
- 实现智能地图服务切换
- 添加瓦片加载监控和容错机制
- 支持多个全球地图服务
- 添加地图服务健康检查
- 完善错误处理和重试机制
