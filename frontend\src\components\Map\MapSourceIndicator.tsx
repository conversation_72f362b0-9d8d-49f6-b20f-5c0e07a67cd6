import React from 'react';
import { Tag, Tooltip } from 'antd';
import { EnvironmentOutlined } from '@ant-design/icons';

interface MapSourceIndicatorProps {
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const MapSourceIndicator: React.FC<MapSourceIndicatorProps> = ({
  position = 'bottom-right',
}) => {
  // 高德地图服务信息
  const serviceInfo = {
    name: '高德地图',
    description: '中国境内详细地图数据',
    icon: <EnvironmentOutlined />,
    color: 'blue',
  };



  // 位置样式
  const positionStyles = {
    'top-left': { top: '10px', left: '10px' },
    'top-right': { top: '10px', right: '10px' },
    'bottom-left': { bottom: '10px', left: '10px' },
    'bottom-right': { bottom: '10px', right: '10px' },
  };

  return (
    <div
      style={{
        position: 'absolute',
        ...positionStyles[position],
        zIndex: 1000,
      }}
    >
      {/* 高德地图源指示器 */}
      <Tooltip title={serviceInfo.description}>
        <Tag
          icon={serviceInfo.icon}
          color={serviceInfo.color}
          style={{
            margin: 0,
            padding: '4px 8px',
            fontSize: '12px',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          }}
        >
          {serviceInfo.name}
        </Tag>
      </Tooltip>
    </div>
  );
};

export default MapSourceIndicator;
