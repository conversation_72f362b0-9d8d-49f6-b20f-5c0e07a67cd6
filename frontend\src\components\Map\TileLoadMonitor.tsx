import React, { useEffect, useRef, useCallback } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import { message } from 'antd';
import { MapServiceType, getFallbackServices } from '../../config/mapConfig';

interface TileLoadMonitorProps {
  currentService: MapServiceType;
  onServiceFallback?: (newService: MapServiceType, reason: string) => void;
  enableAutoFallback?: boolean;
}

interface TileLoadStats {
  total: number;
  loaded: number;
  failed: number;
  failureRate: number;
}

const TileLoadMonitor: React.FC<TileLoadMonitorProps> = ({
  currentService,
  onServiceFallback,
  enableAutoFallback = true,
}) => {
  const map = useMap();
  const statsRef = useRef<TileLoadStats>({ total: 0, loaded: 0, failed: 0, failureRate: 0 });
  const failureTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastServiceRef = useRef<MapServiceType>(currentService);

  // 重置统计数据
  const resetStats = useCallback(() => {
    statsRef.current = { total: 0, loaded: 0, failed: 0, failureRate: 0 };
  }, []);

  // 检查是否需要降级到备用服务
  const checkForFallback = useCallback(() => {
    const stats = statsRef.current;
    
    // 如果失败率超过50%且总请求数超过10个，触发降级
    if (stats.total > 10 && stats.failureRate > 0.5) {
      const fallbackServices = getFallbackServices(currentService);
      
      if (fallbackServices.length > 0 && enableAutoFallback) {
        const nextService = fallbackServices[0];
        console.warn(`瓦片加载失败率过高 (${(stats.failureRate * 100).toFixed(1)}%)，切换到备用服务: ${nextService}`);
        
        message.warning({
          content: `地图加载异常，已自动切换到备用服务`,
          duration: 3,
        });
        
        onServiceFallback?.(nextService, `瓦片加载失败率: ${(stats.failureRate * 100).toFixed(1)}%`);
        resetStats();
      }
    }
  }, [currentService, enableAutoFallback, onServiceFallback, resetStats]);

  // 延迟检查失败率，避免频繁触发
  const scheduleFailureCheck = useCallback(() => {
    if (failureTimeoutRef.current) {
      clearTimeout(failureTimeoutRef.current);
    }
    
    failureTimeoutRef.current = setTimeout(() => {
      checkForFallback();
    }, 2000); // 2秒后检查
  }, [checkForFallback]);

  // 监听瓦片加载事件
  useEffect(() => {
    if (!map) return;

    const handleTileLoadStart = () => {
      statsRef.current.total += 1;
    };

    const handleTileLoad = () => {
      statsRef.current.loaded += 1;
      statsRef.current.failureRate = statsRef.current.failed / statsRef.current.total;
    };

    const handleTileError = (e: L.TileErrorEvent) => {
      statsRef.current.failed += 1;
      statsRef.current.failureRate = statsRef.current.failed / statsRef.current.total;
      
      console.warn('瓦片加载失败:', e.tile.src, e.error);
      
      // 尝试重新加载失败的瓦片（最多重试2次）
      const tile = e.tile as HTMLImageElement & { retryCount?: number };
      if (!tile.retryCount) {
        tile.retryCount = 0;
      }
      
      if (tile.retryCount < 2) {
        tile.retryCount += 1;
        setTimeout(() => {
          tile.src = tile.src; // 重新加载
        }, 1000 * tile.retryCount); // 递增延迟
      }
      
      scheduleFailureCheck();
    };

    // 添加事件监听器
    map.on('tileloadstart', handleTileLoadStart);
    map.on('tileload', handleTileLoad);
    map.on('tileerror', handleTileError);

    return () => {
      map.off('tileloadstart', handleTileLoadStart);
      map.off('tileload', handleTileLoad);
      map.off('tileerror', handleTileError);
      
      if (failureTimeoutRef.current) {
        clearTimeout(failureTimeoutRef.current);
      }
    };
  }, [map, scheduleFailureCheck]);

  // 当地图服务变化时重置统计
  useEffect(() => {
    if (lastServiceRef.current !== currentService) {
      resetStats();
      lastServiceRef.current = currentService;
      
      if (failureTimeoutRef.current) {
        clearTimeout(failureTimeoutRef.current);
      }
    }
  }, [currentService, resetStats]);

  // 开发环境下显示加载统计信息
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const logStats = () => {
        const stats = statsRef.current;
        if (stats.total > 0) {
          console.log(`瓦片加载统计 [${currentService}]:`, {
            总数: stats.total,
            成功: stats.loaded,
            失败: stats.failed,
            失败率: `${(stats.failureRate * 100).toFixed(1)}%`
          });
        }
      };

      const interval = setInterval(logStats, 10000); // 每10秒记录一次
      return () => clearInterval(interval);
    }
  }, [currentService]);

  return null; // 这是一个纯逻辑组件，不渲染任何内容
};

export default TileLoadMonitor;
