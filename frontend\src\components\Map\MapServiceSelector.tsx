import React from 'react';
import { Select, Tooltip } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';
import { MapServiceType } from '../../config/mapConfig';

const { Option } = Select;

interface MapServiceSelectorProps {
  value: MapServiceType;
  onChange: (service: MapServiceType) => void;
  style?: React.CSSProperties;
  size?: 'small' | 'middle' | 'large';
}

/**
 * 地图服务选择器组件
 * 提供符合中国地图审查要求的地图服务选项
 */
const MapServiceSelector: React.FC<MapServiceSelectorProps> = ({
  value,
  onChange,
  style,
  size = 'middle',
}) => {
  const mapServiceOptions = [
    {
      value: MapServiceType.AMAP,
      label: '高德地图',
      description: '高德地图服务（符合中国地图审查要求）',
      official: false,
    },
  ];

  return (
    <div style={style}>
      <Tooltip title="选择符合中国地图审查要求的地图服务">
        <Select
          value={value}
          onChange={onChange}
          size={size}
          style={{ minWidth: 120 }}
          suffixIcon={<GlobalOutlined />}
          placeholder="选择地图服务"
        >
          {mapServiceOptions.map((option) => (
            <Option key={option.value} value={option.value}>
              <div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <span>{option.label}</span>
                  {option.official && (
                    <span
                      style={{
                        fontSize: '10px',
                        color: '#52c41a',
                        backgroundColor: '#f6ffed',
                        border: '1px solid #b7eb8f',
                        borderRadius: '2px',
                        padding: '0 4px',
                      }}
                    >
                      官方
                    </span>
                  )}
                </div>
                <div
                  style={{
                    fontSize: '12px',
                    color: '#666',
                    marginTop: '2px',
                  }}
                >
                  {option.description}
                </div>
              </div>
            </Option>
          ))}
        </Select>
      </Tooltip>
    </div>
  );
};

export default MapServiceSelector;
