import React from 'react';
import { Tag, Tooltip } from 'antd';
import { GlobalOutlined, EnvironmentOutlined } from '@ant-design/icons';
import { MapServiceType } from '../../config/mapConfig';

interface MapSourceIndicatorProps {
  currentService: MapServiceType;
  onServiceChange?: (service: MapServiceType) => void;
  showSwitcher?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const MapSourceIndicator: React.FC<MapSourceIndicatorProps> = ({
  currentService,
  onServiceChange,
  showSwitcher = false,
  position = 'bottom-right',
}) => {
  // 地图服务信息配置
  const serviceInfo = {
    [MapServiceType.AMAP]: {
      name: '高德地图',
      description: '中国境内详细地图数据',
      icon: <EnvironmentOutlined />,
      color: 'blue',
    },
    [MapServiceType.OPENSTREETMAP]: {
      name: 'OpenStreetMap',
      description: '全球开源地图数据',
      icon: <GlobalOutlined />,
      color: 'green',
    },
    [MapServiceType.CARTODB]: {
      name: 'CartoDB',
      description: '简洁全球地图样式',
      icon: <GlobalOutlined />,
      color: 'purple',
    },
    [MapServiceType.ESRI_WORLD]: {
      name: 'ESRI世界地图',
      description: '稳定可靠的全球地图',
      icon: <GlobalOutlined />,
      color: 'geekblue',
    },
    [MapServiceType.STAMEN]: {
      name: 'Stamen',
      description: '简约美观的全球地图',
      icon: <GlobalOutlined />,
      color: 'magenta',
    },
    [MapServiceType.TIANDITU_SATELLITE]: {
      name: '天地图卫星',
      description: '官方卫星影像地图',
      icon: <EnvironmentOutlined />,
      color: 'orange',
    },
    [MapServiceType.TIANDITU_VECTOR]: {
      name: '天地图矢量',
      description: '官方矢量地图',
      icon: <EnvironmentOutlined />,
      color: 'cyan',
    },
    [MapServiceType.BAIDU]: {
      name: '百度地图',
      description: '百度地图服务',
      icon: <EnvironmentOutlined />,
      color: 'red',
    },
  };

  const currentInfo = serviceInfo[currentService];

  // 位置样式
  const positionStyles = {
    'top-left': { top: '10px', left: '10px' },
    'top-right': { top: '10px', right: '10px' },
    'bottom-left': { bottom: '10px', left: '10px' },
    'bottom-right': { bottom: '10px', right: '10px' },
  };

  // 可选的地图服务列表（用于切换器）
  const availableServices = [
    MapServiceType.AMAP,
    MapServiceType.ESRI_WORLD,
    MapServiceType.CARTODB,
    MapServiceType.OPENSTREETMAP,
    MapServiceType.STAMEN,
  ];

  return (
    <div
      style={{
        position: 'absolute',
        ...positionStyles[position],
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        gap: '4px',
      }}
    >
      {/* 当前地图源指示器 */}
      <Tooltip title={currentInfo.description}>
        <Tag
          icon={currentInfo.icon}
          color={currentInfo.color}
          style={{
            margin: 0,
            padding: '4px 8px',
            fontSize: '12px',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            cursor: showSwitcher ? 'pointer' : 'default',
          }}
        >
          {currentInfo.name}
        </Tag>
      </Tooltip>

      {/* 地图源切换器 */}
      {showSwitcher && onServiceChange && (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '2px',
            background: 'white',
            padding: '4px',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            maxHeight: '200px',
            overflowY: 'auto',
          }}
        >
          {availableServices
            .filter(service => service !== currentService)
            .map(service => {
              const info = serviceInfo[service];
              return (
                <Tooltip key={service} title={info.description}>
                  <Tag
                    icon={info.icon}
                    color={info.color}
                    style={{
                      margin: 0,
                      padding: '2px 6px',
                      fontSize: '11px',
                      cursor: 'pointer',
                      opacity: 0.7,
                      transition: 'opacity 0.2s',
                    }}
                    onClick={() => onServiceChange(service)}
                    onMouseEnter={(e) => {
                      (e.target as HTMLElement).style.opacity = '1';
                    }}
                    onMouseLeave={(e) => {
                      (e.target as HTMLElement).style.opacity = '0.7';
                    }}
                  >
                    {info.name}
                  </Tag>
                </Tooltip>
              );
            })}
        </div>
      )}
    </div>
  );
};

export default MapSourceIndicator;
