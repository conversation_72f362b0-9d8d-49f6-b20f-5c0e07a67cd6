import React, { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { getMapConfig, MapServiceType } from '../../config/mapConfig';

// 修复Leaflet默认图标问题
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface BaseMapProps {
  center?: [number, number];
  zoom?: number;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  onMapReady?: (map: L.Map) => void;
}

// 地图事件处理组件
const MapEventHandler: React.FC<{
  onMapReady?: (map: L.Map) => void;
}> = ({ onMapReady }) => {
  const map = useMap();

  useEffect(() => {
    if (map && onMapReady) {
      onMapReady(map);
    }
  }, [map, onMapReady]);

  return null;
};

const BaseMap: React.FC<BaseMapProps> = ({
  center = [30.0, 120.0],
  zoom = 5,
  style = { height: '500px', width: '100%' },
  children,
  onMapReady,
}) => {
  // 获取高德地图配置
  const mapConfig = getMapConfig(MapServiceType.AMAP);
  const { base, annotation } = mapConfig;

  return (
    <MapContainer
      center={center}
      zoom={zoom}
      style={style}
      scrollWheelZoom={true}
      zoomControl={true}
    >
      {/* 底图瓦片层 */}
      <TileLayer
        attribution={base.attribution}
        url={base.url}
        subdomains={base.subdomains}
        maxZoom={base.maxZoom}
        minZoom={base.minZoom}
      />

      {/* 注记层（如果存在） */}
      {annotation && (
        <TileLayer
          attribution={annotation.attribution}
          url={annotation.url}
          subdomains={annotation.subdomains}
          maxZoom={annotation.maxZoom}
          minZoom={annotation.minZoom}
        />
      )}

      {/* 地图事件处理 */}
      <MapEventHandler onMapReady={onMapReady} />

      {/* 子组件 */}
      {children}
    </MapContainer>
  );
};

export default BaseMap;
