/**
 * 地图配置文件
 * 只提供高德地图街道图配置
 */

export interface MapTileConfig {
  name: string;
  url: string;
  attribution: string;
  subdomains?: string[];
  maxZoom?: number;
  minZoom?: number;
  requiresKey?: boolean;
}

export interface MapLayerConfig {
  base: MapTileConfig;
  annotation?: MapTileConfig;
}

/**
 * 高德地图配置
 * 符合中国地图审查要求的商业地图服务，只提供街道图
 */
export const getAMapConfig = (): MapLayerConfig => {
  return {
    base: {
      name: '高德地图',
      url: 'https://webrd0{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}',
      attribution: '&copy; <a href="https://www.amap.com/">高德地图</a>',
      subdomains: ['1', '2', '3', '4'],
      maxZoom: 18,
      minZoom: 3,
      requiresKey: false, // 基础瓦片服务不需要密钥
    },
  };
};



/**
 * 地图服务类型枚举
 */
export enum MapServiceType {
  AMAP = 'amap',
}

/**
 * 获取地图配置
 */
export const getMapConfig = (
  serviceType: MapServiceType = MapServiceType.AMAP
): MapLayerConfig => {
  return getAMapConfig();
};

/**
 * 默认地图配置 - 使用高德地图
 */
export const DEFAULT_MAP_CONFIG = getMapConfig(MapServiceType.AMAP);

/**
 * 地图中心点配置（中国区域）
 */
export const MAP_CENTER_CHINA: [number, number] = [35.0, 104.0];

/**
 * 地图缩放级别配置
 */
export const MAP_ZOOM_LEVELS = {
  COUNTRY: 4,    // 国家级别
  PROVINCE: 6,   // 省级别
  CITY: 10,      // 城市级别
  DISTRICT: 13,  // 区县级别
  STREET: 16,    // 街道级别
};


